/**
 * AI 大模型组件，基于 Spring AI 拓展
 *
 * models 包路径：
 *  1. xinghuo 包：【讯飞】星火，自己实现
 *  2. deepseek 包：【深度求索】DeepSeek，自己实现
 *  3. doubao 包：【字节豆包】DouBao，自己实现
 *  4. hunyuan 包：【腾讯混元】HunYuan，自己实现
 *  5. siliconflow 包：【硅基硅流】SiliconFlow，自己实现
 *  6. midjourney 包：Midjourney API，对接 https://github.com/novicezk/midjourney-proxy 实现
 *  7. suno 包：Suno API，对接 https://github.com/gcui-art/suno-api 实现
 */
package cn.iocoder.yudao.module.ai.framework.ai;